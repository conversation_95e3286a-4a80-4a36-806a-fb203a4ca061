// import Feature3 from '@hi7/assets/icon/feature5.svg';
import InsightImage from '@hi7/assets/background/insight-6.png';
import Feature1 from '@hi7/assets/icon/feature6.svg';
import Feature2 from '@hi7/assets/icon/feature7.svg';
import Feature3 from '@hi7/assets/icon/feature8.svg';
import Feature4 from '@hi7/assets/icon/feature9.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function Feature({ dictionary }: DictionaryProps) {

  const features = [
    {
      icon: Feature1,

    },
    {
      icon: Feature2,

    },
    {
      icon: Feature3,

    },
    {
      icon: Feature4,

    }
  ];

  return (
    <TriggerAnimation>
      <section className="relative overflow-x-clip text-[#047AFF] flex flex-col items-center justify-between gap-10 px-10 py-10 lg:flex-row lg:px-20 lg:pb-20 lg:pt-35 xl:h-[100vh]">
        <svg
          className='absolute inset-0 z-[-1] pointer-events-none'
          width="100%"
          height="100vh"
          viewBox="0 0 1441 856"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
          preserveAspectRatio="none"
        >
          <path d="M0.5 126.95V161.11C0.5 227.91 54.6688 282.06 121.492 282.06H265C463 282.06 587.372 389 587.372 593C587.372 623.331 587.375 615.5 587.375 616.335C587.375 698 639 756 731.5 756H1289.5C1377.5 756 1441 695.5 1441 608V126.95C1441 60.1496 1441 85.9995 1441 85.9995H0.51001C0.51001 85.9995 0.51001 60.1496 0.51001 126.95H0.5Z" fill="#E9F3FF" />
          <path d="M1 80C1 121.974 35.0264 156 77 156H1365C1406.97 156 1441 121.974 1441 80C1441 38.0264 1406.97 4 1365 4H77C35.0263 4 1 38.0264 1 80Z" fill="#E9F3FF" />
        </svg>
        <div className="relative w-full lg:w-1/2 mb-10 lg:mb-0 lg:self-start">
          {/* <h2 className={`text-[40px] lg:text-5xl leading-[45px] font-bold mb-10 ${arsenal.className} md:hidden`}>{dictionary.home.feature2.title.split(' ').map((line, i) => <h2 key={i}>{line}<br /></h2>)}</h2>
          <h2 className={`hidden ml-10 leading-[45px] font-bold mb-10 ${arsenal.className} md:flex lg:text-[64px]`}>{dictionary.home.feature2.title} </h2>
          <p>{dictionary.home.feature2.desc}</p> */}
          <Image src={InsightImage} alt={'insight-image'} className="w-[72%] absolute -right-22 -top-25 lg:w-full lg:rounded-[200px] lg:-left-2 lg:top-50 lg:-translate-x-1/3" />
        </div>

        <div className="w-full grid gap-6 -mt-8 lg:mt-0 lg:w-5/7 xl:w-5/9 xl:self-start">
          <div className="grid gap-8 md:grid-cols-2 xl:gap-20">
            {features.map((feature, i) => (
              <div key={i} className='mb-5'>
                <div className="flex items-center justify-start gap-5 font-[500] text-[24px] leading-[30px]">
                  <feature.icon className="w-14 h-14 lg:w-15 lg:h-15 " />
                  {/* <span className="items-start">{feature.title}</span> */}
                </div>
                <hr className="my-5 xl:my-3" />
                {/* <p className="text-[18px] font-[400] lg:text-[16px] xl:text-[18px]">{feature.desc}</p> */}
              </div>
            ))}
          </div>
        </div>
      </section>
    </TriggerAnimation>
  );
}

export default Feature;
