'use client';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { Locale } from '@hi7/lib/i18n';
import Feature from './Feature';
import WhyChooseUs from './WhyChooseUs';
import { useCoordinatedAnimation } from './useCoordinatedAnimation';

interface CoordinatedAnimationWrapperProps {
  dictionary: Dictionary;
  locale: Locale;
}

export default function CoordinatedAnimationWrapper({ 
  dictionary, 
  locale 
}: CoordinatedAnimationWrapperProps) {
  const { shouldAnimateFeature, shouldAnimateWhyChooseUs, whyChooseUsRef } = useCoordinatedAnimation();

  return (
    <>
      <Feature 
        dictionary={dictionary} 
        locale={locale} 
        shouldSlideDown={shouldAnimateFeature}
      />
      <WhyChooseUs 
        dictionary={dictionary} 
        locale={locale} 
        shouldSlideUp={shouldAnimateWhyChooseUs}
        animationRef={whyChooseUsRef}
      />
    </>
  );
}
