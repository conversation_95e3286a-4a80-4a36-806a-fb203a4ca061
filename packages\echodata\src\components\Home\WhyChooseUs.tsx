import BubbleTrain from '@hi7/assets/background/bubble-train.svg';
import QuestionMark from '@hi7/assets/icon/question-mark.svg';
import Reason1 from '@hi7/assets/icon/reason1.svg';
import Reason2 from '@hi7/assets/icon/reason2.svg';
import Reason3 from '@hi7/assets/icon/reason3.svg';
import Reason4 from '@hi7/assets/icon/reason4.svg';
import Reason5 from '@hi7/assets/icon/reason5.svg';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import AnimationFrame from '../AnimationFrame';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
});

function WhyChooseUs({ dictionary }: DictionaryProps) {

  const reasons = [
    {
      icon: Reason1,
      title: dictionary.home.whychooseus.marketingCost.title,
      desc: dictionary.home.whychooseus.marketingCost.desc,

    },
    {
      icon: Reason2,
      title: dictionary.home.whychooseus.highQuality.title,
      desc: dictionary.home.whychooseus.highQuality.desc,
    },
    {
      icon: Reason3,
      title: dictionary.home.whychooseus.businessOpportunities.title,
      desc: dictionary.home.whychooseus.businessOpportunities.desc,
    },
    {
      icon: Reason4,
      title: dictionary.home.whychooseus.closeDeals.title,
      desc: dictionary.home.whychooseus.closeDeals.desc,
    },
    {
      icon: Reason5,
      title: dictionary.home.whychooseus.userExperience.title,
      desc: dictionary.home.whychooseus.userExperience.desc,
    },
  ];

  return (
    <TriggerAnimation>
      <div className="flex items-center justify-center bg-[#E9F3FF] text-black rounded-[50px] lg:rounded-l-[120px] lg:rounded-r-[0px] lg:py-25 xl:h-screen xl:rounded-l-[150px] overflow-x-clip xl:pt-0">
        <div className="w-full">
          <div className="relative lg:min-h-[560px]">
            <div className="flex flex-col items-center justify-center py-8 pt-12 pb-12 px-10 lg:pb-[60px] lg:items-start lg:px-20 lg:pt-0 xl:pt-[40px] xl:px-30">
              <h2 className={`${arsenal.className} text-left text-red-400 lg:animate-home-feature-1-title text-center text-[37px] leading-[45px] font-bold lg:translate-y-[-100%] lg:text-[64px] lg:leading-[58px] lg:opacity-0 xl:text-[60px]`}>
                {dictionary.home.whychooseus.title}
              </h2>
            </div>
            <div className="text-[#047AFF] px-10 grid items-start justify-center gap-y-[48px] lg:grid-cols-[310px_310px_310px] lg:gap-x-[80px] lg:px-0 xl:grid-cols-[400px_400px_400px] xl:justify-start xl:pl-30">
              {
                reasons.length > 0 && (
                  reasons.map((reason, index) => (
                    <div className="lg:animate-home-help-0 flex flex-col items-start justify-center gap-2 lg:translate-x-[-100%] lg:opacity-0 z-1">
                      <reason.icon />
                      <h2 className="text-[24px] font-medium mt-1 lg:text-[24px]">{reason.title}</h2>
                      <hr className="w-full" />
                      <p className="text-[18px] font-light mt-1 lg:text-[18px]">{reason.desc}</p>
                    </div>
                  ))
                )
              }
            </div>
            <AnimationFrame
              variant="RotateIn"
              once={false}
              className="hidden md:flex h-full w-full items-center justify-end ml-10 -mb-10 lg:absolute lg:top-5 lg:-right-40 xl:top-10 xl:-right-50 z-0"
            >
              <QuestionMark className="h-65 w-65 lg:h-150 lg:w-150 xl:h-200 xl:w-200 fill-current text-black" />
            </AnimationFrame>

            <div className="flex h-full w-full items-center justify-end ml-10 -mb-10 md:hidden z-0">
              <QuestionMark className="h-65 w-65 lg:h-150 lg:w-150 xl:h-200 xl:w-200 fill-current text-black" />
            </div>
          </div>
          <BubbleTrain className="scale-30 rotate-45 fill-current absolute animate-infinite-slide z-[-1] -left-150 lg:scale-75 lg:-left-1/2 xl:-left-1/3 " />
        </div>
      </div>

    </TriggerAnimation>
  );
}

export default WhyChooseUs;
