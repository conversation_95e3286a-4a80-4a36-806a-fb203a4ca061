'use client';

import React, { useEffect, useRef, useState } from 'react';

const PageWrapper = ({ children }: { children: React.ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768); // Tailwind's 'md' breakpoint
    };

    handleResize(); // Set initial state
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault(); // Prevent default scroll behavior

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }

      const direction = event.deltaY > 0 ? 1 : -1;
      const currentScrollY = window.pageYOffset;

      // Find the current active section based on scroll position
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (currentScrollY >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      const nextSectionIndex = currentSectionIndex + direction;

      // Handle scrolling past the last snap section
      if (nextSectionIndex >= sectionsRef.current.length) {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth',
        });
        return;
      }

      // Handle scrolling before the first snap section
      if (nextSectionIndex < 0) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
        return;
      }

      const targetSection = sectionsRef.current[nextSectionIndex];
      const targetPosition = targetSection.offsetTop;
      const startPosition = currentScrollY;
      const distance = targetPosition - startPosition;
      const duration = 200; // 200ms for a smoother scroll
      let startTime: number | null = null;

      const animateScroll = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);

        if (timeElapsed < duration) {
          animationFrameId.current = requestAnimationFrame(animateScroll);
        } else {
          // Ensure we land exactly on the target
          window.scrollTo(0, targetPosition);
          animationFrameId.current = null;
          setActiveSection(nextSectionIndex);
        }
      };

      animationFrameId.current = requestAnimationFrame(animateScroll);
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b; // Ease in cubic
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b; // Ease out cubic
    };

    if (!isMobile) {
      window.addEventListener('wheel', handleWheel, { passive: false });
    }

    return () => {
      window.removeEventListener('wheel', handleWheel);
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isMobile]); // Re-run effect when isMobile changes

  return (
    <div>
      {React.Children.map(children, (child, index) => (
        <div
          ref={(el) => {
            if (el) sectionsRef.current[index] = el;
          }}
        >
          {React.cloneElement(child as React.ReactElement, { isActive: index === activeSection })}
        </div>
      ))}
    </div>
  );
};

export default PageWrapper;
