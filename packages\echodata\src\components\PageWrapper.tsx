'use client';

import React, { useEffect, useRef, useState } from 'react';

const PageWrapper = ({ children }: { children: React.ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const isScrolling = useRef<boolean>(false);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768); // Tailwind's 'md' breakpoint
    };

    handleResize(); // Set initial state
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    // Shared scroll logic for all input methods
    const scrollToSection = (direction: number) => {
      // Set scrolling flag
      isScrolling.current = true;

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }

      const currentScrollY = window.pageYOffset;

      // Find the current active section based on scroll position
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (currentScrollY >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      const nextSectionIndex = currentSectionIndex + direction;

      // Handle scrolling past the last snap section
      if (nextSectionIndex >= sectionsRef.current.length) {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth',
        });
        isScrolling.current = false; // Clear scrolling flag
        return;
      }

      // Handle scrolling before the first snap section
      if (nextSectionIndex < 0) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
        isScrolling.current = false; // Clear scrolling flag
        return;
      }

      const targetSection = sectionsRef.current[nextSectionIndex];
      const targetPosition = targetSection.offsetTop;
      const startPosition = currentScrollY;
      const distance = targetPosition - startPosition;
      const duration = 200; // 200ms for a smoother scroll
      let startTime: number | null = null;

      const animateScroll = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);

        if (timeElapsed < duration) {
          animationFrameId.current = requestAnimationFrame(animateScroll);
        } else {
          // Ensure we land exactly on the target
          window.scrollTo(0, targetPosition);
          animationFrameId.current = null;
          isScrolling.current = false; // Clear scrolling flag
          setActiveSection(nextSectionIndex);
        }
      };

      animationFrameId.current = requestAnimationFrame(animateScroll);
    };

    // Handle mouse wheel and trackpad scrolling
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault(); // Prevent default scroll behavior

      // Skip if already scrolling to prevent conflicts
      if (isScrolling.current) {
        return;
      }

      // Treat trackpad and mouse wheel the same for immediate response
      const direction = event.deltaY > 0 ? 1 : -1;
      scrollToSection(direction);
    };

    // Handle keyboard navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default behavior for navigation keys
      if (
        [
          'ArrowUp',
          'ArrowDown',
          'PageUp',
          'PageDown',
          'Space',
          'Home',
          'End',
        ].includes(event.code)
      ) {
        event.preventDefault();
      }

      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          scrollToSection(1);
          break;
        case 'ArrowUp':
        case 'PageUp':
          scrollToSection(-1);
          break;
        case 'Home':
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setActiveSection(0);
          break;
        case 'End':
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth',
          });
          setActiveSection(sectionsRef.current.length - 1);
          break;
      }
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b; // Ease in cubic
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b; // Ease out cubic
    };

    // Add event listeners for desktop only
    if (!isMobile) {
      // Desktop: mouse wheel, trackpad, and keyboard
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      // Clean up all event listeners
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isMobile]); // Re-run effect when isMobile changes

  return (
    <div>
      {React.Children.map(children, (child, index) => (
        <div
          ref={(el) => {
            if (el) sectionsRef.current[index] = el;
          }}
        >
          {React.cloneElement(child as React.ReactElement, {
            isActive: index === activeSection,
          })}
        </div>
      ))}
    </div>
  );
};

export default PageWrapper;
