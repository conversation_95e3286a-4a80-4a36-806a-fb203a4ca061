'use client';

import React, { useEffect, useRef, useState } from 'react';

const PageWrapper = ({ children }: { children: React.ReactNode }) => {
  const sectionsRef = useRef<HTMLDivElement[]>([]);
  const animationFrameId = useRef<number | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [activeSection, setActiveSection] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768); // Tailwind's 'md' breakpoint
    };

    handleResize(); // Set initial state
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  useEffect(() => {
    // Shared scroll logic for all input methods
    const scrollToSection = (direction: number) => {
      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }

      const currentScrollY = window.pageYOffset;

      // Find the current active section based on scroll position
      let currentSectionIndex = 0;
      for (let i = 0; i < sectionsRef.current.length; i++) {
        if (currentScrollY >= sectionsRef.current[i].offsetTop - 100) {
          currentSectionIndex = i;
        } else {
          break;
        }
      }

      const nextSectionIndex = currentSectionIndex + direction;

      // Handle scrolling past the last snap section
      if (nextSectionIndex >= sectionsRef.current.length) {
        window.scrollTo({
          top: document.documentElement.scrollHeight,
          behavior: 'smooth',
        });
        return;
      }

      // Handle scrolling before the first snap section
      if (nextSectionIndex < 0) {
        window.scrollTo({
          top: 0,
          behavior: 'smooth',
        });
        return;
      }

      const targetSection = sectionsRef.current[nextSectionIndex];
      const targetPosition = targetSection.offsetTop;
      const startPosition = currentScrollY;
      const distance = targetPosition - startPosition;
      const duration = 200; // 200ms for a smoother scroll
      let startTime: number | null = null;

      const animateScroll = (currentTime: number) => {
        if (startTime === null) startTime = currentTime;
        const timeElapsed = currentTime - startTime;
        const run = ease(timeElapsed, startPosition, distance, duration);
        window.scrollTo(0, run);

        if (timeElapsed < duration) {
          animationFrameId.current = requestAnimationFrame(animateScroll);
        } else {
          // Ensure we land exactly on the target
          window.scrollTo(0, targetPosition);
          animationFrameId.current = null;
          setActiveSection(nextSectionIndex);
        }
      };

      animationFrameId.current = requestAnimationFrame(animateScroll);
    };

    // Handle mouse wheel and trackpad scrolling
    const handleWheel = (event: WheelEvent) => {
      event.preventDefault(); // Prevent default scroll behavior

      // Treat trackpad and mouse wheel the same for immediate response
      const direction = event.deltaY > 0 ? 1 : -1;
      scrollToSection(direction);
    };

    // Handle keyboard navigation
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent default behavior for navigation keys
      if (
        [
          'ArrowUp',
          'ArrowDown',
          'PageUp',
          'PageDown',
          'Space',
          'Home',
          'End',
        ].includes(event.code)
      ) {
        event.preventDefault();
      }

      switch (event.code) {
        case 'ArrowDown':
        case 'PageDown':
        case 'Space':
          scrollToSection(1);
          break;
        case 'ArrowUp':
        case 'PageUp':
          scrollToSection(-1);
          break;
        case 'Home':
          window.scrollTo({ top: 0, behavior: 'smooth' });
          setActiveSection(0);
          break;
        case 'End':
          window.scrollTo({
            top: document.documentElement.scrollHeight,
            behavior: 'smooth',
          });
          setActiveSection(sectionsRef.current.length - 1);
          break;
      }
    };

    // Handle touch events for mobile swipe
    let touchStartY = 0;
    let touchEndY = 0;

    const handleTouchStart = (event: TouchEvent) => {
      touchStartY = event.touches[0].clientY;
    };

    const handleTouchEnd = (event: TouchEvent) => {
      touchEndY = event.changedTouches[0].clientY;
      const deltaY = touchStartY - touchEndY;

      // Minimum swipe distance to trigger navigation
      if (Math.abs(deltaY) > 50) {
        const direction = deltaY > 0 ? 1 : -1;
        scrollToSection(direction);
      }
    };

    const ease = (t: number, b: number, c: number, d: number) => {
      t /= d / 2;
      if (t < 1) return (c / 2) * t * t * t + b; // Ease in cubic
      t -= 2;
      return (c / 2) * (t * t * t + 2) + b; // Ease out cubic
    };

    // Add event listeners based on device type
    if (!isMobile) {
      // Desktop: mouse wheel, trackpad, and keyboard
      window.addEventListener('wheel', handleWheel, { passive: false });
      window.addEventListener('keydown', handleKeyDown);
    } else {
      // Mobile: touch events
      window.addEventListener('touchstart', handleTouchStart, {
        passive: true,
      });
      window.addEventListener('touchend', handleTouchEnd, { passive: true });
    }

    return () => {
      // Clean up all event listeners
      window.removeEventListener('wheel', handleWheel);
      window.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('touchstart', handleTouchStart);
      window.removeEventListener('touchend', handleTouchEnd);

      if (animationFrameId.current) {
        cancelAnimationFrame(animationFrameId.current);
      }
    };
  }, [isMobile]); // Re-run effect when isMobile changes

  return (
    <div>
      {React.Children.map(children, (child, index) => (
        <div
          ref={(el) => {
            if (el) sectionsRef.current[index] = el;
          }}
        >
          {React.cloneElement(child as React.ReactElement, {
            isActive: index === activeSection,
          })}
        </div>
      ))}
    </div>
  );
};

export default PageWrapper;
