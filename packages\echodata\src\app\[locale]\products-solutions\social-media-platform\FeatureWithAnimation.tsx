'use client';

import type { Dictionary } from '@hi7/interface/dictionary';
import type { Locale } from '@hi7/lib/i18n';
import { useEffect, useRef, useState } from 'react';
import Feature from './Feature';

interface FeatureWithAnimationProps {
  dictionary: Dictionary;
  locale: Locale;
}

export default function FeatureWithAnimation({ 
  dictionary, 
  locale 
}: FeatureWithAnimationProps) {
  const [shouldSlideDown, setShouldSlideDown] = useState(false);
  const whyChooseUsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // When WhyChooseUs comes into view, trigger Feature slide down
            setShouldSlideDown(true);
          } else {
            // Reset animation when out of view
            setShouldSlideDown(false);
          }
        });
      },
      {
        threshold: 0.3, // Trigger when 30% of the component is visible
        rootMargin: '-100px 0px', // Start animation 100px before entering viewport
      }
    );

    // Find the WhyChooseUs component in the DOM
    const whyChooseUsElement = document.querySelector('[data-component="why-choose-us"]');
    if (whyChooseUsElement) {
      observer.observe(whyChooseUsElement);
    }

    return () => {
      if (whyChooseUsElement) {
        observer.unobserve(whyChooseUsElement);
      }
    };
  }, []);

  return (
    <Feature 
      dictionary={dictionary} 
      locale={locale} 
      shouldSlideDown={shouldSlideDown}
    />
  );
}
