import type { Target, Transition } from 'motion/react';

export type VariantType =
  | 'SlideIn'
  | 'SlideOut'
  | 'SlideUp'
  | 'SlideUpSlow'
  | 'FadeIn'
  | 'SlideDown'
  | 'SlideDownSlow'
  | 'SlideDownDelay'
  | 'RotateIn'
  | 'SlideIn45Degree'
  | 'SlideUpAtEase'
  | 'SlideToRight';

type MotionConfig = {
  initial: Target;
  animate: Target;
  transition: Transition;
};

export const defaultAnimateConfig: Record<VariantType, MotionConfig> = {
  SlideIn: {
    initial: { x: 100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideOut: {
    initial: { x: -100, opacity: 0 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideUp: {
    initial: { y: 100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },
  SlideUpSlow: {
    initial: { y: 100, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.5, delay: 0.2, ease: 'easeOut' },
  },
  SlideDown: {
    initial: { y: -450, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.1 },
  },
  SlideDownSlow: {
    initial: { y: -100, opacity: 1 },
    animate: { y: 100, opacity: 0.3 },
    transition: { duration: 1.5, delay: 0, ease: 'easeOut' },
  },
  SlideDownDelay: {
    initial: { y: -250, opacity: 0 },
    animate: { y: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.5 },
  },

  FadeIn: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },

  RotateIn: {
    initial: { rotate: -50, opacity: 0 },
    animate: { rotate: 0, opacity: 1 },
    transition: { duration: 1, ease: 'easeInOut' },
  },

  SlideIn45Degree: {
    initial: { x: -200, y: 200, opacity: 0 },
    animate: { x: 130, y: -50, opacity: 1 },
    transition: { duration: 0.8, delay: 0.1 },
  },

  SlideUpAtEase: {
    initial: { y: 50 },
    animate: { y: -260 },
    transition: { duration: 0.6, ease: 'easeOut' },
  },

  SlideToRight: {
    initial: { x: '-43vw', opacity: 1 },
    animate: { x: 0, opacity: 1 },
    transition: { duration: 1.2, delay: 0.5 },
  },
};
