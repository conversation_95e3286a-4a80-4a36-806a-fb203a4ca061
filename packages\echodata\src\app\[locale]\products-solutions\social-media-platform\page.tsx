import type { Dictionary } from '@hi7/interface/dictionary';
import type { LocaleProps } from '@hi7/interface/i18n';
import { getDictionary } from '@hi7/lib/i18n';
import Feature from './Feature';
import GetStarted from './GetStarted';
import Hero from './Hero';
import MainHighlight from './MainHighlight';
import WhyChooseUs from './WhyChooseUs';

import PageWrapper from '@hi7/components/PageWrapper';

async function page({ params }: LocaleProps) {
  const { locale } = params;
  const dictionary: Dictionary = await getDictionary(locale);
  return (
    <PageWrapper>
      <Hero dictionary={dictionary} />
      <Feature dictionary={dictionary} locale={locale} />
      <WhyChooseUs dictionary={dictionary} locale={locale} />
      <MainHighlight dictionary={dictionary} locale={locale} />
      <GetStarted dictionary={dictionary} />
    </PageWrapper>
  );
}

export default page;
