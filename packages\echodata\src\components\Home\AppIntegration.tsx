'use client';
import HomepageSection2Img from '@hi7/assets/background/homepage-section2-img.jpg';
import SupportedPlatforms from '@hi7/components/SupportedPlatforms';
import type { DictionaryProps } from '@hi7/interface/i18n';
import { Arsenal } from 'next/font/google';
import Image from 'next/image';
import TriggerAnimation from '../TriggerAnimation';

const arsenal = Arsenal({
  subsets: ['latin'],
  weight: ['400', '700'],
  display: 'swap',
});

function AppIntergration({ dictionary }: DictionaryProps) {
  return (
    <TriggerAnimation>
      <div className="flex flex-col md:flex-col lg:flex-col-reverse">
        <section className="bg-[#90c4fc] pb-20 lg:pb-0 xl:pt-10 rounded-r-[40px] lg:rounded-r-[100px] xl:rounded-r-[130px] relative h-[545px] lg:h-screen xl:h-[875px] mt-0 lg:-mt-65 xl:-mt-90">
          <div className="pl-0 pt-12 lg:pt-12 xl:pt-6 pr-6 lg:pr-22 xl:pr-34 text-right">
            <div className='mb-9 lg:mb-8 xl:mb-9'><h1 className={`text-[42px] lg:text-[64px] font-semibold text-[#0F172A] tracking-[1px] ${arsenal.className}`}>App Integration</h1></div>
            <p className="text-[18px] lg:text-[19px] xl:text-3xl text-gray-700 mt-2 xl:mt-0 mb-5 lg:mb-23 xl:mb-42 leading-[25px] xl:leading-[40px] tracking-[0px] text-thin">
              Access High-quality Marketing Data <br />
              Effortlessly with <strong>60+ Popular Platforms</strong>
              <br /> and<strong>  Applications </strong>integration
            </p>

            <div className="flex flex-wrap justify-center gap-4 mt-4 xl:mt-[125px]">
              <div className="relative absolute left-0 top-0 h-full w-full">
                <hr className='my-2 lg:mt-2 lg:mb-0 xl:mb-5' />
                <SupportedPlatforms location="1st" dictionary={dictionary} />
                <hr className='my-2 lg:mt-2 lg:mb-0 xl:mb-5' />
                <SupportedPlatforms location="2nd" dictionary={dictionary} />
                <hr className='my-2 lg:mt-2 xl:mt-3 lg:mb-0' />
              </div>
            </div>
          </div>
        </section>

        {/* Image Section with Top Rounded Corner */}
        <section className="-mt-25 lg:mt-0">
          <div className="overflow-hidden">
            <div className="relative absolute left-0 top-0 xl:left-15 xl:top-0 w-full h-full lg:max-w-[850px] lg:max-h-[450px] xl:max-w-[1150px] lg:max-h-[650px] xl:max-h-[590px]">
              <Image
                src={HomepageSection2Img}
                alt={'discussion-image'}
                className="-translate-x-38 xl:-translate-x-43 border-20 lg:border-t-0 lg:border-45 xl:border-50 rounded-[50px] lg:rounded-[100px] border-white lg:object-cover h-[315px] lg:h-[380px] xl:h-[480px]"
                priority
              />
            </div>
          </div>
        </section>
      </div>
    </TriggerAnimation>
  );
}

export default AppIntergration;
