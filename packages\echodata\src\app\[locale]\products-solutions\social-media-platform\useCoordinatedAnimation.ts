'use client';

import { useEffect, useRef, useState } from 'react';

export const useCoordinatedAnimation = () => {
  const [shouldAnimateFeature, setShouldAnimateFeature] = useState(false);
  const [shouldAnimateWhyChooseUs, setShouldAnimateWhyChooseUs] = useState(false);
  const whyChooseUsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // When WhyChooseUs comes into view, trigger both animations
            setShouldAnimateFeature(true);
            setShouldAnimateWhyChooseUs(true);
          } else {
            // Reset animations when out of view
            setShouldAnimateFeature(false);
            setShouldAnimateWhyChooseUs(false);
          }
        });
      },
      {
        threshold: 0.3, // Trigger when 30% of the component is visible
        rootMargin: '-100px 0px', // Start animation 100px before entering viewport
      }
    );

    if (whyChooseUsRef.current) {
      observer.observe(whyChooseUsRef.current);
    }

    return () => {
      if (whyChooseUsRef.current) {
        observer.unobserve(whyChooseUsRef.current);
      }
    };
  }, []);

  return {
    shouldAnimateFeature,
    shouldAnimateWhyChooseUs,
    whyChooseUsRef,
  };
};
