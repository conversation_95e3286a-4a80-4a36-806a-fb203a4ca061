@import 'tailwindcss';
@import './animate.home.css';
@import './animate.product-1.css';
@import './animate.product-2.css';
@import './animate.product-3.css';
@import './animate.product-4.css';
@import './animate.price.css';
@import './animate.contact.css';
@import './animate.help.css';
@import './animate.download.css';

@layer base {
  body,
  html {
    scroll-behavior: smooth;
  }

  input:-webkit-autofill,
  textarea:-webkit-autofill,
  select:-webkit-autofill {
    transition: background-color 5000s ease-in-out;
  }

  input:is(:-webkit-autofill, :autofill) + label,
  .hi7-dropdown input:is(:-webkit-autofill, :autofill) + label,
  textarea:is(:-webkit-autofill, :autofill) + label {
    top: -0.938rem;
  }

  input:not([type='radio']):not([type='checkbox']),
  textarea {
    -webkit-border-radius: 0;
    border-radius: 0;
  }
}

.font-arsenal {
  font-family: var(--font-arsenal), serif;
}

@theme {
  --animate-infinite-slider: infiniteSlider 30s linear infinite;
  @keyframes infiniteSlider {
    0% {
      transform: translateX(calc(-250px * 15));
    }

    100% {
      transform: translateX(0);
    }
  }

  @keyframes infiniteSlide {
    0% {
      transform: translateX(0);
    }

    100% {
      transform: translateX(-50%);
    }
  }

  --animate-fab-latest-news: fabLatestNews 0.3s ease-in-out forwards;
  @keyframes fabLatestNews {
    0% {
      transform: translateX(-50%);
    }

    100% {
      transform: translateY(0);
    }
  }

  --animate-fab-latest-contact: fabLatestContact 1s ease-in-out forwards;
  @keyframes fabLatestContact {
    0% {
      opacity: 0;
    }

    100% {
      opacity: 1;
    }
  }

  --animate-fab-latest-contact-reserve: fabLatestContactReserve 1s ease-in-out
    forwards;
  @keyframes fabLatestContactReserve {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }

  --animate-slide-in-left: slideInLeft 0.6s ease-in-out forwards;
  @keyframes slideInLeft {
    from {
      transform: translateX(-50%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}

@utility animation-delay-* {
  animation-delay: --value(integer) ms;
}
/* use for animation delay */
[data-trigger-animation],
[data-trigger-animation] * {
  animation: none !important;
}
